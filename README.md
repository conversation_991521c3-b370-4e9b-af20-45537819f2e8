# Bolltrans - مترجم النصوص الذكي

إضافة متصفح قوية لترجمة النصوص المحددة في أي موقع ويب بسهولة وسرعة.

## المميزات

### 🌐 ترجمة فورية
- ترجمة أي نص محدد في أي صفحة ويب
- دعم أكثر من 15 لغة مختلفة
- كشف تلقائي للغة المصدر

### 📸 التقاط الشاشة والترجمة (جديد!)
- التقاط أي منطقة من الشاشة مثل Windows Snipping Tool
- استخراج النص تلقائياً من الصور باستخدام OCR
- ترجمة فورية للنص المستخرج
- اختصار سريع: Ctrl+Shift+T

### 🎯 سهولة الاستخدام
- واجهة بسيطة وسهلة الاستخدام
- تحديد النص والنقر على زر الترجمة
- عرض النتائج في صندوق أنيق ومنظم
- تحديد المناطق بالسحب والإفلات

### 🔧 إعدادات متقدمة
- اختيار اللغة المستهدفة والمصدر
- تبديل بين مزودي خدمة الترجمة
- حفظ الإعدادات تلقائياً
- تخصيص اختصارات لوحة المفاتيح

### 📱 تصميم متجاوب
- يعمل على جميع أحجام الشاشات
- تصميم عربي أصيل يدعم RTL
- رسوم متحركة سلسة

## التثبيت

### 1. تحميل الملفات
```bash
git clone https://github.com/your-username/bolltrans.git
cd bolltrans
```

### 2. إضافة الأيقونات
قم بإضافة الأيقونات التالية في مجلد `icons/`:
- `icon16.png` (16x16 بكسل)
- `icon32.png` (32x32 بكسل)  
- `icon48.png` (48x48 بكسل)
- `icon128.png` (128x128 بكسل)

### 3. تثبيت الإضافة في المتصفح

#### Chrome/Edge:
1. افتح `chrome://extensions/`
2. فعّل "وضع المطور" (Developer mode)
3. انقر على "تحميل إضافة غير مُعبأة" (Load unpacked)
4. اختر مجلد المشروع

#### Firefox:
1. افتح `about:debugging`
2. انقر على "This Firefox"
3. انقر على "Load Temporary Add-on"
4. اختر ملف `manifest.json`

## كيفية الاستخدام

### الترجمة العادية
1. **حدد النص**: حدد أي نص في أي صفحة ويب
2. **انقر على الترجمة**: سيظهر زر "🌐 ترجم" بجانب النص المحدد
3. **اعرض النتيجة**: ستظهر الترجمة في صندوق منبثق أنيق

### التقاط الشاشة والترجمة 📸
1. **ابدأ التقاط**: اضغط Ctrl+Shift+T أو انقر على زر "التقاط الشاشة"
2. **حدد المنطقة**: اسحب بالماوس لتحديد المنطقة المراد ترجمتها
3. **انتظر المعالجة**: سيتم استخراج النص وترجمته تلقائياً
4. **اعرض النتيجة**: ستظهر النتيجة مع خيارات النسخ والمشاركة

### الإعدادات
- انقر على أيقونة الإضافة في شريط الأدوات
- اختر اللغة المستهدفة من القائمة المنسدلة
- حدد مزود خدمة الترجمة المفضل
- فعّل الكشف التلقائي للغة إذا رغبت

### الميزات الإضافية
- **نسخ الترجمة**: انقر على زر "📋 نسخ"
- **الاستماع للترجمة**: انقر على زر "🔊 استماع"
- **اختبار الترجمة**: استخدم زر الاختبار في الإعدادات
- **التقاط سريع**: استخدم Ctrl+Shift+T في أي وقت

## مزودو خدمة الترجمة

### MyMemory (مجاني)
- **المميزات**: مجاني تماماً، لا يتطلب مفتاح API
- **القيود**: 1000 كلمة يومياً
- **الجودة**: جيدة للاستخدام العادي

### Google Translate (مدفوع)
- **المميزات**: جودة ترجمة عالية، دعم شامل للغات
- **المتطلبات**: مفتاح Google Cloud Translation API
- **التكلفة**: حسب الاستخدام

## هيكل المشروع

```
bolltrans/
├── manifest.json          # تعريف الإضافة
├── content.js            # سكريبت التفاعل مع الصفحات
├── background.js         # سكريبت الخلفية
├── popup.html           # واجهة الإعدادات
├── popup.js             # منطق واجهة الإعدادات
├── popup.css            # تنسيق واجهة الإعدادات
├── styles.css           # تنسيق واجهة الترجمة
├── icons/               # أيقونات الإضافة
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md           # هذا الملف
```

## التطوير

### إضافة لغات جديدة
لإضافة لغة جديدة، قم بتحديث القوائم في:
- `popup.html` (قوائم اللغات)
- `background.js` (رموز اللغات)

### إضافة مزود ترجمة جديد
1. أضف الدالة في `background.js`
2. حدث قائمة المزودين في `popup.html`
3. أضف المنطق في `translateText()`

### تخصيص التصميم
- عدّل `styles.css` لواجهة الترجمة
- عدّل `popup.css` لواجهة الإعدادات

## الأمان والخصوصية

- **لا تخزين للبيانات**: لا يتم حفظ النصوص المترجمة
- **اتصال آمن**: جميع طلبات API تتم عبر HTTPS
- **صلاحيات محدودة**: الإضافة تطلب الحد الأدنى من الصلاحيات

## المساهمة

نرحب بمساهماتكم! يمكنكم:
- الإبلاغ عن الأخطاء
- اقتراح ميزات جديدة
- تحسين الترجمات
- إضافة لغات جديدة

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

إذا واجهت أي مشاكل أو لديك أسئلة:
- افتح issue في GitHub
- راسلنا على البريد الإلكتروني
- راجع قسم الأسئلة الشائعة

---

**Bolltrans** - اجعل الترجمة أسهل وأسرع! 🌐
