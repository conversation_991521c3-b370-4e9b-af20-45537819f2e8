/* Bolltrans Screen Capture Styles */

/* طبقة التقاط الشاشة */
#bolltrans-capture-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background: rgba(0, 0, 0, 0.3) !important;
  z-index: 999999 !important;
  cursor: crosshair !important;
  user-select: none !important;
  font-family: 'Segoe UI', 'Arabic UI Text', Tahoma, sans-serif !important;
}

/* تعليمات التقاط */
#bolltrans-capture-overlay .capture-instructions {
  position: absolute !important;
  top: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  background: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  padding: 15px 25px !important;
  border-radius: 8px !important;
  font-size: 14px !important;
  text-align: center !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  animation: fadeInDown 0.3s ease !important;
}

/* مربع التحديد */
#bolltrans-capture-overlay .selection-box {
  position: absolute !important;
  border: 2px solid #4CAF50 !important;
  background: rgba(76, 175, 80, 0.1) !important;
  pointer-events: none !important;
  z-index: 1000000 !important;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.5) !important;
}

/* معلومات المنطقة المحددة */
.selection-info {
  position: absolute !important;
  background: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  padding: 5px 10px !important;
  border-radius: 4px !important;
  font-size: 12px !important;
  font-family: monospace !important;
  pointer-events: none !important;
  z-index: 1000001 !important;
}

/* نافذة نتيجة التقاط الشاشة */
#bolltrans-capture-result {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  background: white !important;
  border-radius: 15px !important;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3) !important;
  z-index: 1000001 !important;
  max-width: 600px !important;
  min-width: 450px !important;
  max-height: 80vh !important;
  overflow-y: auto !important;
  font-family: 'Segoe UI', 'Arabic UI Text', Tahoma, sans-serif !important;
  animation: slideInUp 0.4s ease !important;
}

/* رأس نافذة النتيجة */
#bolltrans-capture-result .result-header {
  background: linear-gradient(135deg, #4CAF50, #45a049) !important;
  color: white !important;
  padding: 20px !important;
  text-align: center !important;
  border-radius: 15px 15px 0 0 !important;
}

#bolltrans-capture-result .result-header h3 {
  margin: 0 !important;
  font-size: 18px !important;
  font-weight: bold !important;
}

/* محتوى النتيجة */
#bolltrans-capture-result .result-content {
  padding: 25px !important;
}

/* صناديق النص */
.text-box {
  background: #f8f9fa !important;
  padding: 15px !important;
  border-radius: 8px !important;
  margin: 10px 0 !important;
  border-left: 4px solid #2196F3 !important;
  font-size: 14px !important;
  line-height: 1.6 !important;
  word-wrap: break-word !important;
  max-height: 150px !important;
  overflow-y: auto !important;
}

.text-box.translation {
  background: #f1f8e9 !important;
  border-left-color: #4CAF50 !important;
}

.text-box.original {
  background: #e3f2fd !important;
  border-left-color: #2196F3 !important;
}

/* تسميات النصوص */
.text-label {
  font-weight: bold !important;
  color: #333 !important;
  margin-bottom: 8px !important;
  display: block !important;
  font-size: 14px !important;
}

/* أزرار الإجراءات */
.action-buttons {
  display: flex !important;
  gap: 10px !important;
  margin-top: 20px !important;
  flex-wrap: wrap !important;
}

.action-btn {
  flex: 1 !important;
  min-width: 120px !important;
  padding: 12px 15px !important;
  border: none !important;
  border-radius: 6px !important;
  cursor: pointer !important;
  font-size: 13px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 6px !important;
}

.action-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.action-btn.copy-original {
  background: #e3f2fd !important;
  color: #1976d2 !important;
}

.action-btn.copy-original:hover {
  background: #bbdefb !important;
}

.action-btn.copy-translation {
  background: #f1f8e9 !important;
  color: #2e7d32 !important;
}

.action-btn.copy-translation:hover {
  background: #c8e6c9 !important;
}

.action-btn.speak-btn {
  background: #fff3e0 !important;
  color: #f57c00 !important;
}

.action-btn.speak-btn:hover {
  background: #ffe0b2 !important;
}

.action-btn.close-btn {
  background: #ffebee !important;
  color: #c62828 !important;
}

.action-btn.close-btn:hover {
  background: #ffcdd2 !important;
}

/* حالة التحميل */
.loading-overlay {
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  background: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  padding: 25px 30px !important;
  border-radius: 10px !important;
  text-align: center !important;
  font-family: 'Segoe UI', 'Arabic UI Text', Tahoma, sans-serif !important;
  z-index: 1000002 !important;
}

.loading-spinner {
  width: 30px !important;
  height: 30px !important;
  border: 3px solid rgba(255, 255, 255, 0.3) !important;
  border-top: 3px solid white !important;
  border-radius: 50% !important;
  animation: spin 1s linear infinite !important;
  margin: 0 auto 15px !important;
}

/* رسائل الخطأ والنجاح */
.notification {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  padding: 15px 20px !important;
  border-radius: 6px !important;
  z-index: 1000002 !important;
  font-family: 'Segoe UI', 'Arabic UI Text', Tahoma, sans-serif !important;
  font-size: 14px !important;
  max-width: 350px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  animation: slideInRight 0.3s ease !important;
}

.notification.success {
  background: #4CAF50 !important;
  color: white !important;
}

.notification.error {
  background: #f44336 !important;
  color: white !important;
}

.notification.info {
  background: #2196F3 !important;
  color: white !important;
}

/* الرسوم المتحركة */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translate(-50%, -40%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* تنسيق responsive */
@media (max-width: 768px) {
  #bolltrans-capture-result {
    max-width: 95vw !important;
    min-width: 300px !important;
    margin: 10px !important;
  }
  
  .action-buttons {
    flex-direction: column !important;
  }
  
  .action-btn {
    width: 100% !important;
    min-width: auto !important;
  }
  
  #bolltrans-capture-overlay .capture-instructions {
    max-width: 90vw !important;
    font-size: 12px !important;
    padding: 10px 15px !important;
  }
}

/* تنسيق للنصوص العربية */
.rtl-text {
  direction: rtl !important;
  text-align: right !important;
}

.ltr-text {
  direction: ltr !important;
  text-align: left !important;
}

/* تحسينات إضافية */
.text-box::-webkit-scrollbar {
  width: 6px !important;
}

.text-box::-webkit-scrollbar-track {
  background: transparent !important;
}

.text-box::-webkit-scrollbar-thumb {
  background: #ccc !important;
  border-radius: 3px !important;
}

.text-box::-webkit-scrollbar-thumb:hover {
  background: #999 !important;
}
