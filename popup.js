// Bolltrans Popup Script
// يدير واجهة الإعدادات والتفاعل مع المستخدم

class BolltransPopup {
  constructor() {
    this.settings = {};
    this.init();
  }

  async init() {
    // تحميل الإعدادات الحالية
    await this.loadSettings();
    
    // إعداد مستمعي الأحداث
    this.setupEventListeners();
    
    // تحديث واجهة المستخدم
    this.updateUI();
    
    console.log('Bolltrans Popup: Initialized');
  }

  async loadSettings() {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'getSettings' });
      this.settings = response || {};
      
      // إعداد القيم الافتراضية
      this.settings = {
        targetLanguage: 'ar',
        sourceLanguage: 'auto',
        autoDetect: true,
        apiProvider: 'mymemory',
        ...this.settings
      };
    } catch (error) {
      console.error('Error loading settings:', error);
      this.showStatus('خطأ في تحميل الإعدادات', 'error');
    }
  }

  setupEventListeners() {
    // مستمعي تغيير الإعدادات
    document.getElementById('targetLanguage').addEventListener('change', this.handleSettingChange.bind(this));
    document.getElementById('sourceLanguage').addEventListener('change', this.handleSettingChange.bind(this));
    document.getElementById('apiProvider').addEventListener('change', this.handleSettingChange.bind(this));
    document.getElementById('autoDetect').addEventListener('change', this.handleSettingChange.bind(this));

    // أزرار الإجراءات السريعة
    document.getElementById('captureScreen').addEventListener('click', this.startScreenCapture.bind(this));
    document.getElementById('testTranslation').addEventListener('click', this.testTranslation.bind(this));
    document.getElementById('clearHistory').addEventListener('click', this.clearHistory.bind(this));
  }

  updateUI() {
    // تحديث قيم النماذج
    document.getElementById('targetLanguage').value = this.settings.targetLanguage || 'ar';
    document.getElementById('sourceLanguage').value = this.settings.sourceLanguage || 'auto';
    document.getElementById('apiProvider').value = this.settings.apiProvider || 'mymemory';
    document.getElementById('autoDetect').checked = this.settings.autoDetect !== false;
  }

  async handleSettingChange(event) {
    const { id, value, type, checked } = event.target;
    
    // تحديث الإعدادات
    if (type === 'checkbox') {
      this.settings[id] = checked;
    } else {
      this.settings[id] = value;
    }
    
    // حفظ الإعدادات
    await this.saveSettings();
    
    // إظهار رسالة نجاح
    this.showStatus('تم حفظ الإعدادات', 'success');
  }

  async saveSettings() {
    try {
      await chrome.runtime.sendMessage({
        action: 'saveSettings',
        settings: this.settings
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      this.showStatus('خطأ في حفظ الإعدادات', 'error');
    }
  }

  async startScreenCapture() {
    this.showStatus('بدء التقاط الشاشة...', 'loading');

    try {
      // إغلاق النافذة المنبثقة
      window.close();

      // إرسال رسالة لبدء التقاط الشاشة
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs[0]) {
        await chrome.tabs.sendMessage(tabs[0].id, { action: 'startCapture' });
      }
    } catch (error) {
      this.showStatus('فشل في بدء التقاط الشاشة: ' + error.message, 'error');
    }
  }

  async testTranslation() {
    this.showStatus('جاري اختبار الترجمة...', 'loading');

    const testText = 'Hello, this is a test translation.';

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'translate',
        text: testText,
        targetLang: this.settings.targetLanguage,
        sourceLang: 'en'
      });

      if (response.success) {
        this.showStatus(`اختبار ناجح: "${response.translation}"`, 'success');
      } else {
        this.showStatus('فشل الاختبار: ' + response.error, 'error');
      }
    } catch (error) {
      this.showStatus('خطأ في الاختبار: ' + error.message, 'error');
    }
  }

  async clearHistory() {
    try {
      await chrome.storage.local.clear();
      this.showStatus('تم مسح السجل بنجاح', 'success');
    } catch (error) {
      this.showStatus('خطأ في مسح السجل', 'error');
    }
  }

  showStatus(message, type = 'info') {
    const statusElement = document.getElementById('status');
    const statusIcon = statusElement.querySelector('.status-icon');
    const statusText = statusElement.querySelector('.status-text');
    
    // تحديد الأيقونة والنمط حسب النوع
    const statusConfig = {
      success: { icon: '✅', class: 'status-success' },
      error: { icon: '❌', class: 'status-error' },
      loading: { icon: '⏳', class: 'status-loading' },
      info: { icon: 'ℹ️', class: 'status-info' }
    };
    
    const config = statusConfig[type] || statusConfig.info;
    
    // تحديث المحتوى
    statusIcon.textContent = config.icon;
    statusText.textContent = message;
    
    // إزالة الفئات السابقة وإضافة الجديدة
    statusElement.className = 'status ' + config.class;
    
    // إخفاء الرسالة بعد 3 ثوان (إلا إذا كانت loading)
    if (type !== 'loading') {
      setTimeout(() => {
        statusElement.className = 'status';
        statusIcon.textContent = '✅';
        statusText.textContent = 'جاهز للاستخدام';
      }, 3000);
    }
  }

  // دالة لتحديث إحصائيات الاستخدام
  async updateUsageStats() {
    try {
      const stats = await chrome.storage.local.get(['translationCount', 'lastUsed']);
      const count = stats.translationCount || 0;
      const lastUsed = stats.lastUsed || 'لم يتم الاستخدام بعد';
      
      // يمكن إضافة عرض الإحصائيات في واجهة المستخدم
      console.log(`عدد الترجمات: ${count}, آخر استخدام: ${lastUsed}`);
    } catch (error) {
      console.error('Error loading usage stats:', error);
    }
  }

  // دالة لتصدير الإعدادات
  exportSettings() {
    const dataStr = JSON.stringify(this.settings, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'bolltrans-settings.json';
    link.click();
    
    URL.revokeObjectURL(url);
    this.showStatus('تم تصدير الإعدادات', 'success');
  }

  // دالة لاستيراد الإعدادات
  importSettings(file) {
    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const importedSettings = JSON.parse(e.target.result);
        this.settings = { ...this.settings, ...importedSettings };
        await this.saveSettings();
        this.updateUI();
        this.showStatus('تم استيراد الإعدادات بنجاح', 'success');
      } catch (error) {
        this.showStatus('خطأ في استيراد الإعدادات', 'error');
      }
    };
    reader.readAsText(file);
  }
}

// تشغيل popup عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  new BolltransPopup();
});
