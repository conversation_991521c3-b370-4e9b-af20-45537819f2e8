<!DOCTYPE html>
<html>
<head>
    <title>إنشاء أيقونات Bolltrans</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .icon-container {
            display: inline-block;
            margin: 10px;
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon {
            margin-bottom: 10px;
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1976D2;
        }
    </style>
</head>
<body>
    <h1>إنشاء أيقونات Bolltrans</h1>
    <p>انقر على "إنشاء الأيقونة" لكل حجم، ثم "تحميل" لحفظ الملف.</p>
    
    <div class="icon-container">
        <div class="icon">
            <h3>16x16</h3>
            <canvas id="canvas16" width="16" height="16"></canvas>
        </div>
        <button onclick="createIcon(16)">إنشاء الأيقونة</button>
        <button onclick="downloadIcon(16)">تحميل</button>
    </div>
    
    <div class="icon-container">
        <div class="icon">
            <h3>32x32</h3>
            <canvas id="canvas32" width="32" height="32"></canvas>
        </div>
        <button onclick="createIcon(32)">إنشاء الأيقونة</button>
        <button onclick="downloadIcon(32)">تحميل</button>
    </div>
    
    <div class="icon-container">
        <div class="icon">
            <h3>48x48</h3>
            <canvas id="canvas48" width="48" height="48"></canvas>
        </div>
        <button onclick="createIcon(48)">إنشاء الأيقونة</button>
        <button onclick="downloadIcon(48)">تحميل</button>
    </div>
    
    <div class="icon-container">
        <div class="icon">
            <h3>128x128</h3>
            <canvas id="canvas128" width="128" height="128"></canvas>
        </div>
        <button onclick="createIcon(128)">إنشاء الأيقونة</button>
        <button onclick="downloadIcon(128)">تحميل</button>
    </div>

    <script>
        function createIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const ctx = canvas.getContext('2d');
            
            // مسح الكانفاس
            ctx.clearRect(0, 0, size, size);
            
            // رسم الخلفية
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#2196F3');
            gradient.addColorStop(1, '#1976D2');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // رسم دائرة الكرة الأرضية
            const centerX = size / 2;
            const centerY = size / 2;
            const radius = size * 0.35;
            
            ctx.fillStyle = '#4CAF50';
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fill();
            
            // رسم خطوط الطول والعرض
            ctx.strokeStyle = '#2196F3';
            ctx.lineWidth = size > 32 ? 2 : 1;
            
            // خط الاستواء
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.stroke();
            
            // خطوط الطول
            ctx.beginPath();
            ctx.ellipse(centerX, centerY, radius * 0.3, radius, 0, 0, 2 * Math.PI);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.ellipse(centerX, centerY, radius * 0.6, radius, 0, 0, 2 * Math.PI);
            ctx.stroke();
            
            // خطوط العرض
            ctx.beginPath();
            ctx.moveTo(centerX - radius, centerY - radius * 0.5);
            ctx.quadraticCurveTo(centerX, centerY - radius * 0.3, centerX + radius, centerY - radius * 0.5);
            ctx.stroke();
            
            ctx.beginPath();
            ctx.moveTo(centerX - radius, centerY + radius * 0.5);
            ctx.quadraticCurveTo(centerX, centerY + radius * 0.3, centerX + radius, centerY + radius * 0.5);
            ctx.stroke();
            
            // إضافة رمز الترجمة (سهمين)
            if (size >= 48) {
                ctx.fillStyle = 'white';
                ctx.font = `${size * 0.2}px Arial`;
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('⇄', centerX, centerY + size * 0.25);
            }
        }
        
        function downloadIcon(size) {
            const canvas = document.getElementById(`canvas${size}`);
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // إنشاء جميع الأيقونات تلقائياً
        window.onload = function() {
            createIcon(16);
            createIcon(32);
            createIcon(48);
            createIcon(128);
        };
    </script>
</body>
</html>
