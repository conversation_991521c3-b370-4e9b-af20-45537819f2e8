// Bolltrans Content Script
// يتعامل مع تحديد النص وعرض واجهة الترجمة

class BolltransTranslator {
  constructor() {
    this.isActive = false;
    this.selectedText = '';
    this.translationBox = null;
    this.init();
  }

  init() {
    // إضافة مستمعي الأحداث
    document.addEventListener('mouseup', this.handleTextSelection.bind(this));
    document.addEventListener('keydown', this.handleKeyPress.bind(this));
    
    // استقبال رسائل من background script
    chrome.runtime.onMessage.addListener(this.handleMessage.bind(this));
    
    console.log('Bolltrans: Content script loaded');
  }

  handleTextSelection(event) {
    const selection = window.getSelection();
    const selectedText = selection.toString().trim();
    
    if (selectedText.length > 0) {
      this.selectedText = selectedText;
      this.showTranslationButton(event.pageX, event.pageY);
    } else {
      this.hideTranslationBox();
    }
  }

  handleKeyPress(event) {
    // إخفاء صندوق الترجمة عند الضغط على Escape
    if (event.key === 'Escape') {
      this.hideTranslationBox();
    }
  }

  showTranslationButton(x, y) {
    // إزالة الصندوق السابق إن وجد
    this.hideTranslationBox();
    
    // إنشاء زر الترجمة
    const button = document.createElement('div');
    button.id = 'bolltrans-translate-btn';
    button.innerHTML = '🌐 ترجم';
    button.style.cssText = `
      position: absolute;
      left: ${x}px;
      top: ${y - 40}px;
      background: #4CAF50;
      color: white;
      padding: 8px 12px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-family: Arial, sans-serif;
      z-index: 10000;
      box-shadow: 0 2px 10px rgba(0,0,0,0.3);
      border: none;
      user-select: none;
    `;
    
    button.addEventListener('click', () => {
      this.translateSelectedText();
    });
    
    document.body.appendChild(button);
    this.translationBox = button;
    
    // إخفاء الزر بعد 5 ثوان
    setTimeout(() => {
      this.hideTranslationBox();
    }, 5000);
  }

  async translateSelectedText() {
    if (!this.selectedText) return;
    
    // إظهار حالة التحميل
    this.showLoadingState();
    
    try {
      // إرسال طلب الترجمة إلى background script
      const response = await chrome.runtime.sendMessage({
        action: 'translate',
        text: this.selectedText,
        targetLang: await this.getTargetLanguage()
      });
      
      if (response.success) {
        this.showTranslationResult(response.translation);
      } else {
        this.showError('فشل في الترجمة: ' + response.error);
      }
    } catch (error) {
      this.showError('خطأ في الاتصال: ' + error.message);
    }
  }

  showLoadingState() {
    if (this.translationBox) {
      this.translationBox.innerHTML = '⏳ جاري الترجمة...';
      this.translationBox.style.background = '#FF9800';
    }
  }

  showTranslationResult(translation) {
    if (!this.translationBox) return;
    
    // إنشاء صندوق الترجمة المفصل
    const resultBox = document.createElement('div');
    resultBox.id = 'bolltrans-result-box';
    resultBox.innerHTML = `
      <div class="bolltrans-header">
        <span>🌐 Bolltrans</span>
        <button class="bolltrans-close">×</button>
      </div>
      <div class="bolltrans-content">
        <div class="bolltrans-original">
          <strong>النص الأصلي:</strong>
          <p>${this.selectedText}</p>
        </div>
        <div class="bolltrans-translation">
          <strong>الترجمة:</strong>
          <p>${translation}</p>
        </div>
        <div class="bolltrans-actions">
          <button class="bolltrans-copy">📋 نسخ</button>
          <button class="bolltrans-speak">🔊 استماع</button>
        </div>
      </div>
    `;
    
    resultBox.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      border: 1px solid #ddd;
      border-radius: 10px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      z-index: 10001;
      max-width: 400px;
      min-width: 300px;
      font-family: Arial, sans-serif;
    `;
    
    // إضافة مستمعي الأحداث
    resultBox.querySelector('.bolltrans-close').addEventListener('click', () => {
      this.hideTranslationBox();
    });
    
    resultBox.querySelector('.bolltrans-copy').addEventListener('click', () => {
      this.copyToClipboard(translation);
    });
    
    resultBox.querySelector('.bolltrans-speak').addEventListener('click', () => {
      this.speakText(translation);
    });
    
    // إزالة الزر القديم وإضافة الصندوق الجديد
    this.hideTranslationBox();
    document.body.appendChild(resultBox);
    this.translationBox = resultBox;
  }

  showError(message) {
    if (this.translationBox) {
      this.translationBox.innerHTML = '❌ ' + message;
      this.translationBox.style.background = '#f44336';
    }
  }

  hideTranslationBox() {
    if (this.translationBox) {
      this.translationBox.remove();
      this.translationBox = null;
    }
  }

  async getTargetLanguage() {
    try {
      const result = await chrome.storage.sync.get(['targetLanguage']);
      return result.targetLanguage || 'ar'; // العربية كلغة افتراضية
    } catch (error) {
      return 'ar';
    }
  }

  copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
      // إظهار رسالة نجاح
      const notification = document.createElement('div');
      notification.textContent = '✅ تم النسخ';
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 10px;
        border-radius: 5px;
        z-index: 10002;
        font-family: Arial, sans-serif;
      `;
      document.body.appendChild(notification);
      setTimeout(() => notification.remove(), 2000);
    });
  }

  speakText(text) {
    if ('speechSynthesis' in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = 'ar-SA'; // العربية السعودية
      speechSynthesis.speak(utterance);
    }
  }

  handleMessage(request, sender, sendResponse) {
    if (request.action === 'toggle') {
      this.isActive = !this.isActive;
      sendResponse({ active: this.isActive });
    }
  }
}

// تشغيل المترجم
const translator = new BolltransTranslator();
