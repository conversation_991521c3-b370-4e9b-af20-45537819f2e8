// Bolltrans Screen Capture & OCR
// التقاط الشاشة واستخراج النص والترجمة

class BolltransCapture {
  constructor() {
    this.isCapturing = false;
    this.captureOverlay = null;
    this.startX = 0;
    this.startY = 0;
    this.endX = 0;
    this.endY = 0;
    this.selectionBox = null;
    this.init();
  }

  init() {
    // مستمع اختصار لوحة المفاتيح
    document.addEventListener('keydown', this.handleKeyPress.bind(this));
    
    // مستمع رسائل من background script
    chrome.runtime.onMessage.addListener(this.handleMessage.bind(this));
    
    console.log('Bolltrans Capture: Initialized');
  }

  handleKeyPress(event) {
    // Alt+T للتقاط الشاشة (اختصار أبسط)
    if (event.altKey && event.key === 't') {
      event.preventDefault();
      this.startCapture();
    }

    // Escape لإلغاء التقاط
    if (event.key === 'Escape' && this.isCapturing) {
      this.cancelCapture();
    }
  }

  handleMessage(request, sender, sendResponse) {
    if (request.action === 'startCapture') {
      this.startCapture();
      sendResponse({ success: true });
    }
  }

  async startCapture() {
    if (this.isCapturing) return;
    
    try {
      // طلب إذن التقاط الشاشة
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          mediaSource: 'screen',
          width: { ideal: 1920 },
          height: { ideal: 1080 }
        }
      });
      
      this.isCapturing = true;
      this.createCaptureOverlay();
      
      // إيقاف البث فوراً (نحتاج فقط للإذن)
      stream.getTracks().forEach(track => track.stop());
      
    } catch (error) {
      console.error('Error starting capture:', error);
      this.showError('فشل في بدء التقاط الشاشة');
    }
  }

  createCaptureOverlay() {
    // إنشاء طبقة التقاط شفافة
    this.captureOverlay = document.createElement('div');
    this.captureOverlay.id = 'bolltrans-capture-overlay';
    this.captureOverlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background: rgba(0, 0, 0, 0.3);
      z-index: 999999;
      cursor: crosshair;
      user-select: none;
    `;
    
    // إضافة تعليمات مبسطة
    const instructions = document.createElement('div');
    instructions.style.cssText = `
      position: absolute;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: linear-gradient(135deg, #4CAF50, #45a049);
      color: white;
      padding: 15px 25px;
      border-radius: 8px;
      font-family: Arial, sans-serif;
      font-size: 16px;
      text-align: center;
      box-shadow: 0 4px 15px rgba(0,0,0,0.3);
      animation: fadeInDown 0.3s ease;
    `;
    instructions.innerHTML = `
      <div style="font-weight: bold;">📸 اسحب لتحديد المنطقة</div>
      <div style="font-size: 12px; margin-top: 5px; opacity: 0.9;">سيتم الترجمة تلقائياً • Escape للإلغاء</div>
    `;
    
    this.captureOverlay.appendChild(instructions);
    
    // إضافة مستمعي الأحداث
    this.captureOverlay.addEventListener('mousedown', this.startSelection.bind(this));
    this.captureOverlay.addEventListener('mousemove', this.updateSelection.bind(this));
    this.captureOverlay.addEventListener('mouseup', this.endSelection.bind(this));
    
    document.body.appendChild(this.captureOverlay);
  }

  startSelection(event) {
    this.startX = event.clientX;
    this.startY = event.clientY;
    
    // إنشاء مربع التحديد
    this.selectionBox = document.createElement('div');
    this.selectionBox.style.cssText = `
      position: absolute;
      border: 2px solid #4CAF50;
      background: rgba(76, 175, 80, 0.1);
      pointer-events: none;
      z-index: 1000000;
    `;
    
    this.captureOverlay.appendChild(this.selectionBox);
  }

  updateSelection(event) {
    if (!this.selectionBox) return;
    
    this.endX = event.clientX;
    this.endY = event.clientY;
    
    const left = Math.min(this.startX, this.endX);
    const top = Math.min(this.startY, this.endY);
    const width = Math.abs(this.endX - this.startX);
    const height = Math.abs(this.endY - this.startY);
    
    this.selectionBox.style.left = left + 'px';
    this.selectionBox.style.top = top + 'px';
    this.selectionBox.style.width = width + 'px';
    this.selectionBox.style.height = height + 'px';
  }

  async endSelection(event) {
    if (!this.selectionBox) return;
    
    const left = Math.min(this.startX, this.endX);
    const top = Math.min(this.startY, this.endY);
    const width = Math.abs(this.endX - this.startX);
    const height = Math.abs(this.endY - this.startY);
    
    // التأكد من أن المنطقة كبيرة بما فيه الكفاية
    if (width < 50 || height < 20) {
      this.showError('المنطقة المحددة صغيرة جداً');
      this.cancelCapture();
      return;
    }
    
    // إظهار حالة التحميل
    this.showLoadingState();
    
    try {
      // التقاط المنطقة المحددة
      const imageData = await this.captureSelectedArea(left, top, width, height);
      
      // استخراج النص من الصورة
      const extractedText = await this.extractTextFromImage(imageData);
      
      if (extractedText && extractedText.trim()) {
        // ترجمة النص المستخرج
        await this.translateExtractedText(extractedText);
      } else {
        this.showError('لم يتم العثور على نص في المنطقة المحددة');
      }
      
    } catch (error) {
      console.error('Capture error:', error);
      this.showError('فشل في معالجة الصورة: ' + error.message);
    }
    
    this.cancelCapture();
  }

  async captureSelectedArea(left, top, width, height) {
    return new Promise((resolve, reject) => {
      // استخدام html2canvas لالتقاط المنطقة
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      canvas.width = width;
      canvas.height = height;
      
      // التقاط الشاشة باستخدام Chrome API
      chrome.runtime.sendMessage({
        action: 'captureScreen',
        area: { left, top, width, height }
      }, (response) => {
        if (response.success) {
          resolve(response.imageData);
        } else {
          reject(new Error(response.error));
        }
      });
    });
  }

  async extractTextFromImage(imageData) {
    try {
      // استخدام OCR.space API (مجاني)
      const response = await this.performOCR(imageData);
      return response.text;
    } catch (error) {
      console.error('OCR Error:', error);
      throw new Error('فشل في استخراج النص من الصورة');
    }
  }

  async performOCR(imageData) {
    // إرسال طلب OCR إلى background script
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({
        action: 'performOCR',
        imageData: imageData
      }, (response) => {
        if (response.success) {
          resolve({ text: response.text });
        } else {
          reject(new Error(response.error));
        }
      });
    });
  }

  async translateExtractedText(text) {
    try {
      // الحصول على إعدادات الترجمة
      const settings = await chrome.storage.sync.get(['targetLanguage', 'sourceLanguage']);
      
      // إرسال طلب الترجمة
      const response = await chrome.runtime.sendMessage({
        action: 'translate',
        text: text,
        targetLang: settings.targetLanguage || 'ar',
        sourceLang: settings.sourceLanguage || 'auto'
      });
      
      if (response.success) {
        this.showTranslationResult(text, response.translation);
      } else {
        this.showError('فشل في الترجمة: ' + response.error);
      }
      
    } catch (error) {
      this.showError('خطأ في الترجمة: ' + error.message);
    }
  }

  showTranslationResult(originalText, translation) {
    // إنشاء نافذة عرض النتيجة
    const resultWindow = document.createElement('div');
    resultWindow.id = 'bolltrans-capture-result';
    resultWindow.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      border-radius: 15px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      z-index: 1000001;
      max-width: 500px;
      min-width: 400px;
      font-family: Arial, sans-serif;
      overflow: hidden;
    `;
    
    resultWindow.innerHTML = `
      <div style="background: linear-gradient(135deg, #4CAF50, #45a049); color: white; padding: 20px; text-align: center; border-radius: 15px 15px 0 0;">
        <h3 style="margin: 0; font-size: 20px; font-weight: bold;">✨ تمت الترجمة بنجاح!</h3>
      </div>
      <div style="padding: 25px;">
        <div style="margin-bottom: 20px;">
          <div style="background: #f1f8e9; padding: 15px; border-radius: 10px; border-left: 5px solid #4CAF50; font-size: 16px; line-height: 1.6;">
            ${translation}
          </div>
        </div>
        <details style="margin-bottom: 20px;">
          <summary style="cursor: pointer; color: #666; font-size: 14px; margin-bottom: 10px;">📄 عرض النص الأصلي</summary>
          <div style="background: #f8f9fa; padding: 12px; border-radius: 8px; border-left: 4px solid #2196F3; font-size: 14px; color: #555;">
            ${originalText}
          </div>
        </details>
        <div style="display: flex; gap: 12px;">
          <button id="copy-translation" style="flex: 2; padding: 12px; border: none; border-radius: 8px; background: #4CAF50; color: white; cursor: pointer; font-weight: bold; font-size: 14px;">
            📋 نسخ الترجمة
          </button>
          <button id="copy-original" style="flex: 1; padding: 12px; border: none; border-radius: 8px; background: #e3f2fd; color: #1976d2; cursor: pointer; font-size: 14px;">
            📄 نسخ الأصلي
          </button>
          <button id="close-result" style="padding: 12px 16px; border: none; border-radius: 8px; background: #f5f5f5; color: #666; cursor: pointer; font-size: 14px;">
            ✕
          </button>
        </div>
      </div>
    `;
    
    // إضافة مستمعي الأحداث
    resultWindow.querySelector('#copy-original').addEventListener('click', () => {
      this.copyToClipboard(originalText);
    });
    
    resultWindow.querySelector('#copy-translation').addEventListener('click', () => {
      this.copyToClipboard(translation);
    });
    
    resultWindow.querySelector('#close-result').addEventListener('click', () => {
      resultWindow.remove();
    });
    
    document.body.appendChild(resultWindow);
    
    // إغلاق تلقائي بعد 30 ثانية
    setTimeout(() => {
      if (resultWindow.parentNode) {
        resultWindow.remove();
      }
    }, 30000);
  }

  showLoadingState() {
    if (this.captureOverlay) {
      const loading = document.createElement('div');
      loading.style.cssText = `
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        font-family: Arial, sans-serif;
      `;
      loading.innerHTML = `
        <div style="font-size: 16px; margin-bottom: 10px;">⏳ جاري المعالجة...</div>
        <div style="font-size: 12px;">استخراج النص وترجمته</div>
      `;
      this.captureOverlay.appendChild(loading);
    }
  }

  showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #f44336;
      color: white;
      padding: 15px 20px;
      border-radius: 5px;
      z-index: 1000002;
      font-family: Arial, sans-serif;
      max-width: 300px;
    `;
    errorDiv.textContent = '❌ ' + message;
    
    document.body.appendChild(errorDiv);
    
    setTimeout(() => {
      errorDiv.remove();
    }, 5000);
  }

  copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
      const notification = document.createElement('div');
      notification.textContent = '✅ تم النسخ بنجاح';
      notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 10px 15px;
        border-radius: 5px;
        z-index: 1000002;
        font-family: Arial, sans-serif;
      `;
      document.body.appendChild(notification);
      setTimeout(() => notification.remove(), 2000);
    });
  }

  cancelCapture() {
    this.isCapturing = false;
    if (this.captureOverlay) {
      this.captureOverlay.remove();
      this.captureOverlay = null;
    }
    this.selectionBox = null;
  }
}

// تشغيل نظام التقاط الشاشة
const captureSystem = new BolltransCapture();
