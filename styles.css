/* Bolltrans Styles - تنسيق واجهة الترجمة */

/* أنماط عامة للإضافة */
.bolltrans-element {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  box-sizing: border-box;
  z-index: 999999;
}

/* زر الترجمة */
#bolltrans-translate-btn {
  position: absolute;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  border: none;
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
  transition: all 0.3s ease;
  user-select: none;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  z-index: 999999;
  animation: fadeInUp 0.3s ease;
}

#bolltrans-translate-btn:hover {
  background: linear-gradient(135deg, #45a049, #4CAF50);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
}

/* صندوق نتيجة الترجمة */
#bolltrans-result-box {
  position: fixed;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  max-width: 450px;
  min-width: 320px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  z-index: 999999;
  animation: fadeInScale 0.3s ease;
  overflow: hidden;
}

/* رأس صندوق الترجمة */
.bolltrans-header {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.bolltrans-header span {
  font-size: 16px;
}

.bolltrans-close {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.bolltrans-close:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* محتوى صندوق الترجمة */
.bolltrans-content {
  padding: 16px;
}

.bolltrans-original,
.bolltrans-translation {
  margin-bottom: 16px;
}

.bolltrans-original strong,
.bolltrans-translation strong {
  color: #333;
  font-size: 14px;
  display: block;
  margin-bottom: 8px;
}

.bolltrans-original p,
.bolltrans-translation p {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  margin: 0;
  line-height: 1.5;
  border-left: 4px solid #2196F3;
  font-size: 14px;
  color: #333;
}

.bolltrans-translation p {
  border-left-color: #4CAF50;
  background: #f1f8e9;
}

/* أزرار الإجراءات */
.bolltrans-actions {
  display: flex;
  gap: 8px;
  margin-top: 16px;
}

.bolltrans-actions button {
  flex: 1;
  padding: 10px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.bolltrans-copy {
  background: #e3f2fd;
  color: #1976d2;
}

.bolltrans-copy:hover {
  background: #bbdefb;
}

.bolltrans-speak {
  background: #fff3e0;
  color: #f57c00;
}

.bolltrans-speak:hover {
  background: #ffe0b2;
}

/* الرسوم المتحركة */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* تنسيق responsive */
@media (max-width: 480px) {
  #bolltrans-result-box {
    max-width: 90vw;
    min-width: 280px;
  }
  
  .bolltrans-actions {
    flex-direction: column;
  }
  
  .bolltrans-actions button {
    width: 100%;
  }
}

/* تنسيق للنصوص العربية */
.bolltrans-arabic {
  direction: rtl;
  text-align: right;
  font-family: 'Segoe UI', 'Arabic UI Text', 'Tahoma', sans-serif;
}

/* تنسيق للنصوص الإنجليزية */
.bolltrans-english {
  direction: ltr;
  text-align: left;
}

/* حالة التحميل */
.bolltrans-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #666;
}

.bolltrans-loading::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid #e0e0e0;
  border-top: 2px solid #2196F3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* رسائل الخطأ */
.bolltrans-error {
  background: #ffebee;
  color: #c62828;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #f44336;
  margin: 8px 0;
}

/* رسائل النجاح */
.bolltrans-success {
  background: #e8f5e8;
  color: #2e7d32;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #4caf50;
  margin: 8px 0;
}

/* تنسيق خاص للنصوص الطويلة */
.bolltrans-long-text {
  max-height: 150px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;
}

.bolltrans-long-text::-webkit-scrollbar {
  width: 6px;
}

.bolltrans-long-text::-webkit-scrollbar-track {
  background: transparent;
}

.bolltrans-long-text::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

.bolltrans-long-text::-webkit-scrollbar-thumb:hover {
  background: #999;
}
