{"name": "bolltrans", "version": "1.0.0", "description": "إضافة متصفح ذكية لترجمة النصوص المحددة في أي موقع ويب", "main": "manifest.json", "scripts": {"build": "echo 'Building Bolltrans extension...'", "test": "echo 'Testing Bolltrans extension...'", "lint": "echo 'Linting code...'", "package": "zip -r bolltrans-v1.0.0.zip . -x '*.git*' 'node_modules/*' 'package*.json' 'create-icons.html' 'test.html'", "dev": "echo 'Development mode - Load unpacked extension in browser'"}, "keywords": ["translation", "browser-extension", "arabic", "english", "multilingual", "text-translation", "web-extension", "chrome-extension", "firefox-addon"], "author": {"name": "Bolltrans Team", "email": "<EMAIL>", "url": "https://bolltrans.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/bolltrans/bolltrans-extension.git"}, "bugs": {"url": "https://github.com/bolltrans/bolltrans-extension/issues"}, "homepage": "https://github.com/bolltrans/bolltrans-extension#readme", "engines": {"node": ">=14.0.0"}, "devDependencies": {"web-ext": "^7.0.0", "eslint": "^8.0.0"}, "webExt": {"sourceDir": ".", "artifactsDir": "dist", "ignoreFiles": ["package.json", "package-lock.json", "node_modules", "create-icons.html", "test.html", ".git", "*.md"]}, "manifest": {"version": "1.0.0", "name": "Bolltrans - مترجم النصوص", "description": "إضافة لترجمة النصوص المحددة في أي موقع ويب"}, "features": ["ترجمة فورية للنصوص المحددة", "دعم أكثر من 15 لغة", "كشف تلقائي للغة المصدر", "واجهة عربية أصيلة", "مزودي ترجمة متعددين", "نسخ واستماع للترجمات", "تصميم متجاوب", "إعدادات قابلة للتخصيص"], "supported_browsers": ["Chrome 88+", "Firefox 78+", "Edge 88+", "Safari 14+"], "permissions_explanation": {"activeTab": "للوصول إلى محتوى الصفحة الحالية لترجمة النصوص المحددة", "storage": "لحفظ إعدادات المستخدم وتفضيلات الترجمة", "scripting": "لحقن سكريبت الترجمة في صفحات الويب"}, "api_providers": {"mymemory": {"name": "MyMemory Translation API", "free": true, "limit": "1000 words/day", "quality": "Good"}, "google": {"name": "Google Cloud Translation API", "free": false, "limit": "Pay per use", "quality": "Excellent"}}, "installation": {"chrome": "chrome://extensions/ -> Developer mode -> Load unpacked", "firefox": "about:debugging -> This Firefox -> Load Temporary Add-on", "edge": "edge://extensions/ -> Developer mode -> Load unpacked"}, "development": {"setup": ["1. <PERSON><PERSON> the repository", "2. Open browser extension management page", "3. Enable developer mode", "4. Load unpacked extension", "5. Select the project folder"], "testing": ["1. Open test.html in browser", "2. Select any text sample", "3. Click translate button", "4. Verify translation appears", "5. Test copy and speak features"]}}