<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات Bolltrans</title>
    <style>
        body {
            font-family: 'Segoe UI', 'Arabic UI Text', Tahoma, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            direction: rtl;
        }
        
        .container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 25px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        
        .header p {
            margin: 8px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
        }
        
        .content {
            padding: 30px;
        }
        
        .setting-group {
            margin-bottom: 25px;
        }
        
        .setting-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 15px;
        }
        
        .setting-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            transition: border-color 0.3s ease;
            direction: rtl;
        }
        
        .setting-group select:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }
        
        .shortcut-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
            margin: 20px 0;
        }
        
        .shortcut-info h3 {
            margin: 0 0 10px 0;
            color: #2e7d32;
            font-size: 16px;
        }
        
        .shortcut-info p {
            margin: 5px 0;
            color: #333;
            font-size: 14px;
        }
        
        .shortcut-key {
            background: #fff;
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #ddd;
            font-family: monospace;
            font-weight: bold;
            color: #333;
        }
        
        .save-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        
        .status {
            margin-top: 15px;
            padding: 12px;
            border-radius: 6px;
            text-align: center;
            font-size: 14px;
            display: none;
        }
        
        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border: 1px solid #c8e6c9;
        }
        
        .status.error {
            background: #ffebee;
            color: #c62828;
            border: 1px solid #ffcdd2;
        }
        
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .back-link a {
            color: #4CAF50;
            text-decoration: none;
            font-size: 14px;
        }
        
        .back-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚙️ إعدادات Bolltrans</h1>
            <p>تخصيص تجربة الترجمة حسب احتياجاتك</p>
        </div>
        
        <div class="content">
            <div class="setting-group">
                <label for="targetLanguage">اللغة المستهدفة للترجمة:</label>
                <select id="targetLanguage">
                    <option value="ar">العربية</option>
                    <option value="en">الإنجليزية</option>
                    <option value="tr">التركية</option>
                    <option value="fr">الفرنسية</option>
                    <option value="es">الإسبانية</option>
                    <option value="de">الألمانية</option>
                    <option value="it">الإيطالية</option>
                    <option value="pt">البرتغالية</option>
                    <option value="ru">الروسية</option>
                    <option value="ja">اليابانية</option>
                    <option value="ko">الكورية</option>
                    <option value="zh">الصينية</option>
                    <option value="hi">الهندية</option>
                    <option value="fa">الفارسية</option>
                    <option value="ur">الأردية</option>
                </select>
            </div>

            <div class="setting-group">
                <label for="sourceLanguage">اللغة المصدر:</label>
                <select id="sourceLanguage">
                    <option value="auto">كشف تلقائي (مستحسن)</option>
                    <option value="ar">العربية</option>
                    <option value="en">الإنجليزية</option>
                    <option value="tr">التركية</option>
                    <option value="fr">الفرنسية</option>
                    <option value="es">الإسبانية</option>
                    <option value="de">الألمانية</option>
                    <option value="it">الإيطالية</option>
                    <option value="pt">البرتغالية</option>
                    <option value="ru">الروسية</option>
                    <option value="ja">اليابانية</option>
                    <option value="ko">الكورية</option>
                    <option value="zh">الصينية</option>
                    <option value="hi">الهندية</option>
                    <option value="fa">الفارسية</option>
                    <option value="ur">الأردية</option>
                </select>
            </div>

            <div class="setting-group">
                <label for="apiProvider">مزود خدمة الترجمة:</label>
                <select id="apiProvider">
                    <option value="mymemory">MyMemory (مجاني - مستحسن)</option>
                    <option value="google">Google Translate (يتطلب مفتاح API)</option>
                </select>
            </div>

            <div class="shortcut-info">
                <h3>🎯 الاختصارات السريعة</h3>
                <p><strong>التقاط الشاشة:</strong> <span class="shortcut-key">Alt + T</span></p>
                <p><strong>النقر على الأيقونة:</strong> يبدأ التقاط الشاشة مباشرة</p>
                <p><strong>الإلغاء:</strong> <span class="shortcut-key">Escape</span></p>
            </div>

            <button id="saveSettings" class="save-btn">💾 حفظ الإعدادات</button>
            
            <div id="status" class="status"></div>
            
            <div class="back-link">
                <a href="#" onclick="window.close()">← العودة للمتصفح</a>
            </div>
        </div>
    </div>

    <script>
        class SettingsManager {
            constructor() {
                this.settings = {};
                this.init();
            }

            async init() {
                await this.loadSettings();
                this.setupEventListeners();
                this.updateUI();
            }

            async loadSettings() {
                try {
                    this.settings = await chrome.storage.sync.get([
                        'targetLanguage',
                        'sourceLanguage',
                        'apiProvider'
                    ]);
                    
                    // القيم الافتراضية
                    this.settings = {
                        targetLanguage: 'ar',
                        sourceLanguage: 'auto',
                        apiProvider: 'mymemory',
                        ...this.settings
                    };
                } catch (error) {
                    console.error('Error loading settings:', error);
                }
            }

            setupEventListeners() {
                document.getElementById('saveSettings').addEventListener('click', this.saveSettings.bind(this));
            }

            updateUI() {
                document.getElementById('targetLanguage').value = this.settings.targetLanguage;
                document.getElementById('sourceLanguage').value = this.settings.sourceLanguage;
                document.getElementById('apiProvider').value = this.settings.apiProvider;
            }

            async saveSettings() {
                try {
                    this.settings.targetLanguage = document.getElementById('targetLanguage').value;
                    this.settings.sourceLanguage = document.getElementById('sourceLanguage').value;
                    this.settings.apiProvider = document.getElementById('apiProvider').value;

                    await chrome.storage.sync.set(this.settings);
                    this.showStatus('✅ تم حفظ الإعدادات بنجاح!', 'success');
                } catch (error) {
                    this.showStatus('❌ فشل في حفظ الإعدادات', 'error');
                }
            }

            showStatus(message, type) {
                const statusEl = document.getElementById('status');
                statusEl.textContent = message;
                statusEl.className = `status ${type}`;
                statusEl.style.display = 'block';
                
                setTimeout(() => {
                    statusEl.style.display = 'none';
                }, 3000);
            }
        }

        // تشغيل مدير الإعدادات
        document.addEventListener('DOMContentLoaded', () => {
            new SettingsManager();
        });
    </script>
</body>
</html>
