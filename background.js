// Bolltrans Background Script
// يتعامل مع طلبات الترجمة وإدارة الإعدادات

class BolltransBackground {
  constructor() {
    this.apiKeys = {
      google: '', // يمكن إضافة مفتاح Google Translate API
      mymemory: '' // MyMemory Translation API مجاني
    };
    this.init();
  }

  init() {
    // مستمع رسائل من content scripts
    chrome.runtime.onMessage.addListener(this.handleMessage.bind(this));

    // مستمع اختصارات لوحة المفاتيح
    chrome.commands.onCommand.addListener(this.handleCommand.bind(this));

    // إعداد الإعدادات الافتراضية
    this.initializeSettings();

    console.log('Bolltrans: Background script loaded');
  }

  async initializeSettings() {
    try {
      const settings = await chrome.storage.sync.get([
        'targetLanguage',
        'sourceLanguage',
        'autoDetect',
        'apiProvider'
      ]);
      
      // إعداد القيم الافتراضية
      const defaultSettings = {
        targetLanguage: 'ar', // العربية
        sourceLanguage: 'auto', // كشف تلقائي
        autoDetect: true,
        apiProvider: 'mymemory' // مجاني
      };
      
      // حفظ الإعدادات إذا لم تكن موجودة
      const newSettings = { ...defaultSettings, ...settings };
      await chrome.storage.sync.set(newSettings);
      
    } catch (error) {
      console.error('Error initializing settings:', error);
    }
  }

  async handleCommand(command) {
    if (command === 'capture-and-translate') {
      // إرسال رسالة لبدء التقاط الشاشة
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, { action: 'startCapture' });
      }
    }
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'translate':
          const result = await this.translateText(
            request.text,
            request.targetLang || 'ar',
            request.sourceLang || 'auto'
          );
          sendResponse(result);
          break;

        case 'captureScreen':
          const captureResult = await this.captureScreen(request.area);
          sendResponse(captureResult);
          break;

        case 'performOCR':
          const ocrResult = await this.performOCR(request.imageData);
          sendResponse(ocrResult);
          break;

        case 'getSettings':
          const settings = await chrome.storage.sync.get([
            'targetLanguage',
            'sourceLanguage',
            'autoDetect',
            'apiProvider'
          ]);
          sendResponse(settings);
          break;

        case 'saveSettings':
          await chrome.storage.sync.set(request.settings);
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ error: 'Unknown action' });
      }
    } catch (error) {
      sendResponse({ error: error.message });
    }

    return true; // للاستجابة غير المتزامنة
  }

  async translateText(text, targetLang = 'ar', sourceLang = 'auto') {
    try {
      // الحصول على مزود الترجمة المفضل
      const settings = await chrome.storage.sync.get(['apiProvider']);
      const provider = settings.apiProvider || 'mymemory';
      
      let translation;
      
      switch (provider) {
        case 'google':
          translation = await this.translateWithGoogle(text, targetLang, sourceLang);
          break;
        case 'mymemory':
        default:
          translation = await this.translateWithMyMemory(text, targetLang, sourceLang);
          break;
      }
      
      return {
        success: true,
        translation: translation,
        originalText: text,
        sourceLang: sourceLang,
        targetLang: targetLang
      };
      
    } catch (error) {
      console.error('Translation error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async translateWithMyMemory(text, targetLang, sourceLang) {
    // MyMemory API - مجاني مع حد 1000 كلمة يومياً
    const langPair = sourceLang === 'auto' ? `autodetect|${targetLang}` : `${sourceLang}|${targetLang}`;
    const url = `https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=${langPair}`;
    
    try {
      const response = await fetch(url);
      const data = await response.json();
      
      if (data.responseStatus === 200) {
        return data.responseData.translatedText;
      } else {
        throw new Error('MyMemory API error: ' + data.responseDetails);
      }
    } catch (error) {
      throw new Error('فشل في الاتصال بخدمة الترجمة: ' + error.message);
    }
  }

  async translateWithGoogle(text, targetLang, sourceLang) {
    // Google Translate API - يتطلب مفتاح API
    const apiKey = this.apiKeys.google;
    if (!apiKey) {
      throw new Error('مفتاح Google Translate API غير متوفر');
    }
    
    const url = `https://translation.googleapis.com/language/translate/v2?key=${apiKey}`;
    const body = {
      q: text,
      target: targetLang,
      source: sourceLang === 'auto' ? undefined : sourceLang,
      format: 'text'
    };
    
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body)
      });
      
      const data = await response.json();
      
      if (data.error) {
        throw new Error('Google API error: ' + data.error.message);
      }
      
      return data.data.translations[0].translatedText;
    } catch (error) {
      throw new Error('فشل في الاتصال بـ Google Translate: ' + error.message);
    }
  }

  async captureScreen(area) {
    try {
      // التقاط الشاشة باستخدام Chrome API
      const dataUrl = await chrome.tabs.captureVisibleTab(null, {
        format: 'png',
        quality: 100
      });

      // قص المنطقة المحددة
      const croppedImage = await this.cropImage(dataUrl, area);

      return {
        success: true,
        imageData: croppedImage
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async cropImage(dataUrl, area) {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        const canvas = new OffscreenCanvas(area.width, area.height);
        const ctx = canvas.getContext('2d');

        ctx.drawImage(
          img,
          area.left, area.top, area.width, area.height,
          0, 0, area.width, area.height
        );

        canvas.convertToBlob({ type: 'image/png' }).then(blob => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result);
          reader.readAsDataURL(blob);
        });
      };
      img.src = dataUrl;
    });
  }

  async performOCR(imageData) {
    try {
      // استخدام OCR.space API (مجاني)
      const formData = new FormData();

      // تحويل base64 إلى blob
      const response = await fetch(imageData);
      const blob = await response.blob();

      formData.append('file', blob, 'screenshot.png');
      formData.append('apikey', 'helloworld'); // مفتاح مجاني محدود
      formData.append('language', 'ara'); // العربية والإنجليزية
      formData.append('isOverlayRequired', 'false');
      formData.append('detectOrientation', 'true');
      formData.append('scale', 'true');
      formData.append('OCREngine', '2');

      const ocrResponse = await fetch('https://api.ocr.space/parse/image', {
        method: 'POST',
        body: formData
      });

      const result = await ocrResponse.json();

      if (result.IsErroredOnProcessing) {
        throw new Error(result.ErrorMessage || 'OCR processing failed');
      }

      const extractedText = result.ParsedResults?.[0]?.ParsedText || '';

      return {
        success: true,
        text: extractedText.trim()
      };

    } catch (error) {
      // محاولة استخدام Tesseract.js كبديل
      return await this.performLocalOCR(imageData);
    }
  }

  async performLocalOCR(imageData) {
    try {
      // استخدام Tesseract.js للـ OCR المحلي
      // هذا يتطلب تحميل مكتبة Tesseract.js

      // للآن، سنستخدم طريقة بسيطة لاستخراج النص
      // يمكن تحسينها لاحقاً

      return {
        success: false,
        error: 'OCR المحلي غير متوفر حالياً. يرجى المحاولة مرة أخرى.'
      };

    } catch (error) {
      return {
        success: false,
        error: 'فشل في استخراج النص من الصورة'
      };
    }
  }

  // ترجمة محلية بسيطة للكلمات الشائعة (احتياطية)
  getLocalTranslation(text, targetLang) {
    const commonTranslations = {
      'ar': {
        'hello': 'مرحبا',
        'goodbye': 'وداعا',
        'thank you': 'شكرا لك',
        'please': 'من فضلك',
        'yes': 'نعم',
        'no': 'لا',
        'good': 'جيد',
        'bad': 'سيء',
        'big': 'كبير',
        'small': 'صغير'
      },
      'en': {
        'مرحبا': 'hello',
        'وداعا': 'goodbye',
        'شكرا': 'thank you',
        'من فضلك': 'please',
        'نعم': 'yes',
        'لا': 'no',
        'جيد': 'good',
        'سيء': 'bad',
        'كبير': 'big',
        'صغير': 'small'
      }
    };

    const translations = commonTranslations[targetLang];
    if (translations) {
      const lowerText = text.toLowerCase().trim();
      return translations[lowerText] || null;
    }

    return null;
  }
}

// تشغيل background script
const background = new BolltransBackground();
