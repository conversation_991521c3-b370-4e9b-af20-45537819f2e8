/* Bolltrans Popup Styles - تنسيق واجهة الإعدادات */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', 'Arabic UI Text', Tahoma, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  direction: rtl;
}

.container {
  width: 380px;
  min-height: 500px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

/* Header */
.header {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  color: white;
  padding: 20px;
  text-align: center;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 8px;
}

.icon {
  font-size: 24px;
}

.header h1 {
  font-size: 24px;
  font-weight: bold;
}

.subtitle {
  font-size: 14px;
  opacity: 0.9;
}

/* Settings */
.settings {
  padding: 20px;
}

.setting-group {
  margin-bottom: 16px;
}

.setting-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.setting-group select {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: border-color 0.3s ease;
  direction: rtl;
}

.setting-group select:focus {
  outline: none;
  border-color: #2196F3;
  box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

/* Checkbox */
.checkbox-group {
  margin-top: 20px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: #333;
}

.checkbox-label input[type="checkbox"] {
  margin-left: 10px;
  width: 18px;
  height: 18px;
  accent-color: #2196F3;
}

/* Quick Actions */
.quick-actions {
  padding: 0 20px 20px;
}

.quick-actions h3 {
  font-size: 16px;
  color: #333;
  margin-bottom: 12px;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 8px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.action-btn {
  flex: 1;
  padding: 12px 8px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  background: #f8f9fa;
  color: #333;
}

.action-btn:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-icon {
  font-size: 18px;
}

/* Status */
.status {
  margin: 0 20px 20px;
  padding: 12px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  background: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.status-success {
  background: #e8f5e8;
  color: #2e7d32;
  border-color: #c8e6c9;
}

.status-error {
  background: #ffebee;
  color: #c62828;
  border-color: #ffcdd2;
}

.status-loading {
  background: #fff3e0;
  color: #ef6c00;
  border-color: #ffcc02;
}

.status-info {
  background: #e3f2fd;
  color: #1565c0;
  border-color: #bbdefb;
}

.status-icon {
  font-size: 16px;
}

.status-text {
  flex: 1;
}

/* Instructions */
.instructions {
  padding: 0 20px 20px;
  background: #f8f9fa;
  margin: 0 20px 20px;
  border-radius: 8px;
}

.instructions h3 {
  font-size: 16px;
  color: #333;
  margin-bottom: 12px;
  padding-top: 16px;
}

.instructions ol {
  padding-right: 20px;
  color: #666;
  font-size: 14px;
  line-height: 1.6;
}

.instructions li {
  margin-bottom: 8px;
}

/* Footer */
.footer {
  background: #f8f9fa;
  padding: 16px 20px;
  text-align: center;
  border-top: 1px solid #e0e0e0;
}

.footer p {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.version {
  font-size: 11px;
  color: #999;
}

/* Responsive */
@media (max-width: 400px) {
  .container {
    width: 100%;
    min-height: 100vh;
    border-radius: 0;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-btn {
    flex-direction: row;
    justify-content: center;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.container {
  animation: fadeIn 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Focus styles */
button:focus,
select:focus,
input:focus {
  outline: 2px solid #2196F3;
  outline-offset: 2px;
}

/* Loading animation for buttons */
.action-btn.loading {
  pointer-events: none;
  opacity: 0.7;
}

.action-btn.loading .btn-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
